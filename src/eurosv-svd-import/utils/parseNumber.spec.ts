import { parseAmount, parseInteger, parseNumber } from './parseNumber'

describe('parseAmount', () => {
  it('should parse amount', () => {
    expect(parseAmount('0')).toBe(0)
    expect(parseAmount('123')).toBe(123)
    expect(parseAmount('123.45')).toBe(123.45)
    expect(parseAmount('123,45')).toBe(123.45)
    expect(parseAmount('-123.45')).toBe(-123.45)
    expect(parseAmount(' 123.45 ')).toBe(123.45)
    expect(parseAmount(undefined)).toBe(1)
    expect(parseAmount('')).toBe(1)
    expect(parseAmount('not a number')).toBe(1)
    expect(parseAmount('1e5')).toBe(100000)
    expect(parseAmount('1.23e-4')).toBe(0.000123)
  })
})

describe('parseInteger', () => {
  it('should parse integer', () => {
    expect(parseInteger('0')).toBe(0)
    expect(parseInteger('123')).toBe(123)
    expect(parseInteger('-123')).toBe(-123)
    expect(parseInteger('123.99')).toBe(123)
    expect(parseInteger(' 123 ')).toBe(123)
    expect(parseInteger('010')).toBe(10)
    expect(parseInteger(undefined)).toBeUndefined()
    expect(parseInteger('')).toBeUndefined()
    expect(parseInteger('not a number')).toBeUndefined()
    expect(parseInteger('1e5')).toBe(1)
    expect(parseInteger('1.23e-4')).toBe(1)
  })
})

describe('parseNumber', () => {
  describe('valid string inputs', () => {
    it('should parse integer string', () => {
      expect(parseNumber('0')).toBe(0)
      expect(parseNumber('123')).toBe(123)
      expect(parseNumber('123.45')).toBe(123.45)
      expect(parseNumber('123,45')).toBe(123.45)
      expect(parseNumber('-123,45')).toBe(-123.45)
      expect(parseNumber(' 123.45 ')).toBe(123.45)
      expect(parseNumber('1e5')).toBe(100000)
      expect(parseNumber('1.23e-4')).toBe(0.000123)
      expect(parseNumber(undefined)).toBeUndefined()
      expect(parseNumber('')).toBeUndefined()
      expect(parseNumber('not a number')).toBeUndefined()
    })
  })
})
