import { BadRequestException, Injectable } from '@nestjs/common'
import { AuthService } from 'src/auth/auth.service'
import JSZip from 'jszip'
import * as DAT5 from 'api2dat5'
import mime from 'mime-types'
import { parse } from 'fast-xml-parser'
import { format } from 'date-fns'

import { UserConfigService } from 'src/user-config/user-config.service'
import { CustomerService } from 'src/customer/customer.service'

import { HttpHeaders } from 'src/common/types/common'
import { SvdContent } from './types'
import { parseAmount } from './utils/parseNumber'
import { getVehicleType } from './utils/getVehicleType'
import { getTypeOfInsuranceText } from './utils/getTypeOfInsurance'
import { formatDate, parseDate } from './utils/parseDate'
import { getOperator } from './utils/getOperator'
import { formatCurrency } from './utils/formatCurrency'
import { getCompanyName } from './utils/getCompanyName'
import { getFirstAndLastName } from './utils/getFirstAndLastName'
import { generateUniqClaimName } from './utils/generateUniqClaimName'
import { ProfilesService } from 'src/profiles/profiles.service'
import { parseInteger } from 'src/eurosv-svd-import/utils/parseInteger'

@Injectable()
export class EurosvSvdImportService {
  constructor(
    private authService: AuthService,
    private readonly userConfigService: UserConfigService,
    private readonly profileService: ProfilesService,
    private readonly customerService: CustomerService
  ) {}

  async createContractBySvd(file: Express.Multer.File, headers: HttpHeaders): Promise<any> {
    try {
      const { svd, filesAttachments, imagesAttachments } = await this.getZipFiles(file)
      const token = headers['dat-authorizationtoken']
      const dat5Options: DAT5.Options = {
        credentials: {
          'dat-authorizationtoken': token
        }
      }
      const initialContractData = await this.getInitialContractData(token)
      const createContractResponse = await this.createContract({ svd, dat5Options, initialContractData })
      await this.uploadAttachments({
        attachments: filesAttachments,
        dat5Options,
        contractId: createContractResponse.return,
        folderId: initialContractData.euroSvFilesFolderId || 0
      })
      await this.uploadAttachments({
        attachments: imagesAttachments,
        dat5Options,
        contractId: createContractResponse.return,
        folderId: initialContractData.euroSvImagesFolderId || 0
      })
      return createContractResponse
    } catch (e) {
      if (e?.response?.data) {
        throw new BadRequestException(
          parse(e.response.data)?.['S:Envelope']?.['S:Body']?.['S:Fault']?.faultstring || e?.message
        )
      }
      throw new BadRequestException(e?.message)
    }
  }

  async getInitialContractData(token: string) {
    const { user: username, customerNumber } = this.authService.decodeToken(token)
    const { config } = (await this.userConfigService.getUserConfig(+customerNumber)) || {}
    const templateConfig = config?.templates?.['145621'] || config?.templates?.['default']
    const claimNumberMemoFieldName = templateConfig?.['claim-management']?.incrementalNumberSave || 'claimNumber'
    const userConfig = config?.users?.[username] || config?.users?.default
    const euroSvFilesFolderId = userConfig?.settings?.euroSvFilesFolderId
    const euroSvImagesFolderId = userConfig?.settings?.euroSvImagesFolderId
    const userProfile = await this.profileService.getUserProfileByUsername(token, username)
    const { name = '', surname = '' } = userProfile || {}
    const incrNumber = await this.customerService.autoincrementIncrementalData({ customerNumber, username })
    const { claimNumber } = incrNumber || {}
    return {
      username,
      customerNumber,
      name,
      surname,
      claimNumber: claimNumber || '',
      euroSvFilesFolderId,
      euroSvImagesFolderId,
      claimNumberMemoFieldName
    }
  }

  parseSvd(content: string): SvdContent {
    const lines = content.split(/\r?\n/)
    const parsedData: SvdContent = {}
    let currentSection: string | null = null

    for (const line of lines) {
      if (!line.trim()) continue

      const sectionMatch = line.match(/^\[(.+)]$/)
      if (sectionMatch) {
        currentSection = sectionMatch[1]
        const baseSection = currentSection.replace(/\d+(\.\d+)*$/, '').replace(/\.$/, '')

        if (!parsedData[baseSection]) {
          parsedData[baseSection] = []
        }

        parsedData[baseSection].push({})
      } else if (currentSection) {
        const [key, value] = line.split('=').map(s => s.trim())
        const baseSection = currentSection.replace(/\d+(\.\d+)*$/, '').replace(/\.$/, '')

        if (Array.isArray(parsedData[baseSection])) {
          parsedData[baseSection][parsedData[baseSection].length - 1][key] = value || ''
        } else {
          parsedData[baseSection] = [{ [key]: value || '' }]
        }
      }
    }

    // NOTE: validation is not needed temporarily
    // this.validateSvd(parsedData)
    return parsedData
  }

  validateSvd(svd: SvdContent) {
    const requiredFields = ['ABSENDER', 'INHALT', 'ALG', 'BEG', 'BES', 'FZG', 'PKW_AT', 'WHK', 'WHKEVAL', 'WHKZSF']
    if (requiredFields.some(field => !svd[field])) {
      throw new Error('svd file not euroSv structure')
    }
  }

  async getZipFiles(file: Express.Multer.File) {
    const zip = new JSZip()
    const zipContents = await zip.loadAsync(file.buffer)
    const svdFileName = Object.keys(zipContents.files).find(name => name.endsWith('.svd'))

    if (!svdFileName) {
      throw new Error('.svd file does not existing')
    }
    const attachments = await Promise.all(
      Object.keys(zipContents.files)
        .filter(name => name !== svdFileName)
        .map(async name => {
          const fileData = await zipContents.files[name].async('base64')
          return {
            binaryData: fileData,
            fileName: name,
            mimeType: mime.lookup(name) || 'application/octet-stream'
          } as DAT.AttachmentItem
        })
    )
    const { imagesAttachments, filesAttachments } = attachments.reduce(
      (acc, attachment) => {
        if (/\.(jpg|png|jpeg)$/i.test(attachment.fileName)) {
          acc.imagesAttachments.push(attachment)
        } else if (/\.(pdf)$/i.test(attachment.fileName)) {
          acc.filesAttachments.push(attachment)
        }
        return acc
      },
      { imagesAttachments: <DAT.AttachmentItem[]>[], filesAttachments: <DAT.AttachmentItem[]>[] }
    )

    const svdContent = await zipContents.files[svdFileName].async('text')
    return {
      svd: svdContent,
      imagesAttachments,
      filesAttachments
    }
  }

  buildDossier(svdContent: SvdContent) {
    const alg = svdContent.ALG ? svdContent.ALG[0] : {}
    const fzg = svdContent.FZG ? svdContent.FZG[0] : {}
    const beg = svdContent.BEG ? svdContent.BEG[0] : {}
    const whk = svdContent.WHK ? svdContent.WHK[0] : {}
    const pkw = svdContent.PKW_AT ? svdContent.PKW_AT[0] : {}
    const whkEval = svdContent.WHKEVAL ? svdContent.WHKEVAL[0] : {}
    const whkZsf = svdContent.WHKZSF ? svdContent.WHKZSF[0] : {}

    const timeUnitsPerHour = parseAmount(whk.ST_SPMO)
    const SS_SPMO_LK1 = parseAmount(whk.SS_SPMO_LK1)
    const SS_LACK_LK1 = parseAmount(whk.SS_LACK_LK1)
    const labourWage = timeUnitsPerHour && SS_SPMO_LK1 ? SS_SPMO_LK1 / timeUnitsPerHour : 1
    const parts = beg.BEG_SVNAME ? beg.BEG_SVNAME.split(' ') : null
    const Expert = {
      Title: parts?.[0],
      NameLong: parts?.slice(1, -1).join(' '),
      CompanyName: parts?.at(-1)
    }

    const dossier = {
      Language: 'de_AT',
      Currency: 'EUR',
      Country: 'AT',
      Name: generateUniqClaimName(),
      ...(beg.GUA_NR && { Name: beg.GUA_NR }),
      ...(beg.FREMDID_WERT && { Description: beg.FREMDID_WERT }),
      Vehicle: {
        ...(fzg.FZG_BEWLAND && { Country: fzg.FZG_BEWLAND }),
        // Description: fzg.FZG_ART_TXT,
        ...(fzg.FZG_BEWGRUPPE && { VehicleType: getVehicleType(fzg.FZG_BEWGRUPPE) }),
        ...(!Number.isNaN(Number(pkw.TACHO_IST)) && {
          MileageEstimated: Number(pkw.TACHO_IST),
          MileageOdometer: Number(pkw.TACHO_IST)
        }),
        ...(fzg.FAHRGESTELLNR && { VehicleIdentNumber: fzg.FAHRGESTELLNR }),
        ...(formatDate(pkw.ERSTZULASSUNG) && { InitialRegistration: formatDate(pkw.ERSTZULASSUNG) }),
        RegistrationData: {
          ...(fzg.KENNZEICHEN1 && { LicenseNumber: fzg.KENNZEICHEN1 }),
          ...(fzg.FZG_NATIONALCODE && { NationalCodeAustria: fzg.FZG_NATIONALCODE })
        },
        TechInfo: {
          ...(!Number.isNaN(Number(pkw.HUBRAUM)) && { Capacity: Number(pkw.HUBRAUM) }),
          ...(!Number.isNaN(Number(pkw.LEISTUNG)) && { PowerKw: Number(pkw.LEISTUNG) })
        },
        Equipment: {
          ...(fzg.LACKFARBE && { Color: fzg.LACKFARBE })
        }
      },
      TradingData: {
        ...(beg.BEG_SVNAME && {
          Expert: {
            Title: Expert.Title,
            NameLong: Expert.NameLong,
            CompanyName: Expert.CompanyName
          }
        })
      },
      RepairOrder: {
        ...(parseInteger(alg.DECKUNGSART) && { TypeOfInsurance: parseInteger(alg.DECKUNGSART) }),
        ...(alg.POLIZZENR && { PolicyNumber: alg.POLIZZENR }),
        ...((alg.SCHADENNR || whk.SCHADENNUMMER) && { DamageNumber: alg.SCHADENNR || whk.SCHADENNUMMER }),
        ...(formatDate(whk.SCHADENDATUM) && { DamageDate: formatDate(whk.SCHADENDATUM) })
      },
      RepairCalculation: {
        RepairWages: {
          WageUnitsPerHour: timeUnitsPerHour,
          ...(whk.SS_SPMO_LK1 && {
            CarBody1: labourWage,
            Mechanic1: labourWage,
            Electric1: labourWage
          }),

          ...(SS_LACK_LK1 && {
            Lacquer: SS_LACK_LK1 / timeUnitsPerHour
          })
        },
        RepairParameters: {
          LacquerTimeUnitSystem: 'STD',
          TimeUnitsPerHour: timeUnitsPerHour,
          LacquerTimeUnitsPerHour: timeUnitsPerHour,
          AdditionalCostsFlatAmount: Number(whkZsf.NEBENKOSTENPAUSCHALE) ?? 38.6
        },
        CalculationSummary: {
          SparePartsCosts: {
            ...(whkEval.ERSATZTEILEGESAMT && { AllSum: +whkEval.ERSATZTEILEGESAMT }),
            ...(whkEval.BESCHAFFUNGSKOSTEN && { ProcurementCostsFromParts: +whkEval.BESCHAFFUNGSKOSTEN }),
            ...(whkEval.ENTSORGUNG && { DisposalCostsSpareParts: +whkEval.ENTSORGUNG })
          },
          ...(whkEval.WHKOSTENBRUTTO && { TotalGrossCorrected: whkEval.WHKOSTENBRUTTO }),
          ...(whkZsf.WHKZSF_SUMMENETTO && { TotalNetCorrected: whkZsf.WHKZSF_SUMMENETTO })
        },
        CalcResultCommon: {
          RepairCalculationSummary: {
            ...(!Number.isNaN(Number(whkEval.KLEINMATERIAL)) && {
              SumSmallSparePartCosts: Number(whkEval.KLEINMATERIAL)
            }),
            LabourCosts: {
              ...(!Number.isNaN(Number(whkEval.LOHNERSATZTEILE)) && { TotalSum: Number(whkEval.LOHNERSATZTEILE) })
            },
            LacquerCosts: {
              ...(!Number.isNaN(Number(whkEval.LOHNERSATZTEILE)) && { TotalSum: Number(whkEval.LOHNERSATZTEILE) }),
              Wage: {
                ...(!Number.isNaN(Number(whkEval.LOHNLACKIERUNG)) && { Price: Number(whkEval.LOHNLACKIERUNG) })
              },
              Material: {
                ...(!Number.isNaN(Number(whkEval.LACKMATERIALGESAMT)) && {
                  TotalSum: Number(whkEval.LACKMATERIALGESAMT)
                })
              }
            }
          }
        }
      }
    }

    return dossier as DAT5.MyClaimExternalService_schema2.Dossier
  }

  buildCustomTemplateData(
    svdContent: SvdContent,
    initialContractData: {
      username: string
      customerNumber: string
      name: string
      surname: string
      claimNumber: string
      euroSvFilesFolderId: number | null | undefined
      euroSvImagesFolderId: number | null | undefined
      claimNumberMemoFieldName: string
    }
  ): DAT5.MyClaimExternalService_schema1.createOrUpdateContract_templateData_entry[] {
    const customTemplateData: Record<string, string | undefined> = {}
    customTemplateData['creationDate'] = format(new Date(), 'dd.MM.yy')
    customTemplateData['createdByName'] = `${initialContractData.name} ${initialContractData.surname}`
    customTemplateData['createdByAlias'] = initialContractData.username
    customTemplateData['euroSVCase'] = 'import'
    customTemplateData[initialContractData.claimNumberMemoFieldName] = initialContractData.claimNumber

    const absender = svdContent.ABSENDER ? svdContent.ABSENDER[0] : {}
    const alg = svdContent.ALG ? svdContent.ALG[0] : {}
    const fzg = svdContent.FZG ? svdContent.FZG[0] : {}
    const whk = svdContent.WHK ? svdContent.WHK[0] : {}
    const beg = svdContent.BEG ? svdContent.BEG[0] : {}
    const bes = svdContent.BES ? svdContent.BES[0] : {}
    const pkw = svdContent.PKW_AT ? svdContent.PKW_AT[0] : {}
    const whkZsf = svdContent.WHKZSF ? svdContent.WHKZSF[0] : {}

    const besDatum = parseDate(bes.BES_DATUM)
    let besDatumFormatted = ''
    if (!!besDatum) {
      besDatumFormatted = 'Besichtigungsdatum: ' + formatDate(besDatum, true) + '\n'
      customTemplateData['auditionDate'] = formatDate(besDatum, true)
      customTemplateData['contractNote'] = besDatumFormatted
    }

    customTemplateData['licencePlateInsurant'] = alg.VN_KENNZ
    customTemplateData['importantNote1'] = alg.HINWEIS
    customTemplateData['importantNote2'] = alg.HINWEIS2
    customTemplateData['importantNote3'] = alg.REPWEG_EINSPAR
    customTemplateData['orderDate'] = formatDate(parseDate(alg.AUFTRAGSDATUM), true)
    if (alg.DECKUNGSART) customTemplateData['manfredTypeOfCover'] = getTypeOfInsuranceText(alg.DECKUNGSART)

    if (beg.BEG_WB_EMPFAENGER)
      customTemplateData['residualValueOperator'] = getOperator(beg.BEG_WB_EMPFAENGER)?.toString()
    customTemplateData['contractInformation'] = beg.GUA_INFO
    customTemplateData['contractNumber'] = beg.FREMDID_WERT
    customTemplateData['residualValueAmount'] = beg.DAT_RVP_WERT?.replace('.', ',')
    customTemplateData['residualValueDealer'] = beg.DAT_RVP_NAME?.replace('.', ',')
    customTemplateData['repairable'] = beg.BEG_REPARIERBAR

    customTemplateData['damageNumberLabel'] = whk.SCHADENNUMMER_BEZ
    customTemplateData['firstEstimate'] = formatCurrency(whk.ERSTSCHAETZUNG)
    customTemplateData['damageDate'] = formatDate(parseDate(whk.SCHADENDATUM), true)

    customTemplateData['compensationIBAN'] = whkZsf.ABLOESE_IBAN
    customTemplateData['compensationBank'] = whkZsf.ABLOESE_BANK
    customTemplateData['compensationBIC'] = whkZsf.ABLOESE_BIC
    customTemplateData['compensationValue'] = whkZsf.ABLOESE_WERT?.replace('.', ',')
    customTemplateData['compensationIncludesVAT'] = whkZsf.UST_ABLOESE === '0' ? 'true' : 'false'

    customTemplateData['clientSoftware'] = absender.PROGRAMM

    customTemplateData['lacquerState'] = fzg.LACKZUSTAND
    customTemplateData['vehicleState'] = fzg.ALLGEMEINZUSTAND
    customTemplateData['vehicleToInspect'] = fzg.FZG_MARKE_TXT
    customTemplateData['vbrand'] = fzg.FZG_MARKE_TXT
    customTemplateData['vmodel'] = fzg.FZG_MODELL_TXT
    customTemplateData['vtype'] = fzg.FZG_TYPE_TXT
    customTemplateData['nationalCodeAustria'] = fzg.FZG_NATIONALCODE
    customTemplateData['unloadedWeight'] = fzg.EIGENGEWICHT?.replace('.', ',')
    customTemplateData['vehicleSafteyInspectionUpcomingDate'] = fzg.PLAKETTE_BIS

    customTemplateData['vehicleCC'] = pkw.HUBRAUM
    customTemplateData['vehicleKW'] = pkw.LEISTUNG

    if (whkZsf.SCHADENART) {
      if (whkZsf.SCHADENART === '1') customTemplateData['damageCategory'] = 'J'
      if (whkZsf.SCHADENART === '2') customTemplateData['damageCategory'] = 'G'
    } else if (whk.SCHADENART) {
      if (whkZsf.SCHADENART === '1') customTemplateData['damageCategory'] = 'J'
      if (whkZsf.SCHADENART === '2') customTemplateData['damageCategory'] = 'G'
    }

    if (typeof whk.ANSTOSSBEREICH === 'string') {
      try {
        customTemplateData['impactArea'] = JSON.stringify(whk.ANSTOSSBEREICH.split(''))
      } catch (e) {
        customTemplateData['impactArea'] = whk.ANSTOSSBEREICH
      }
    }

    if (Array.isArray(svdContent.BTG)) {
      for (const [iBTG, svdPerson] of svdContent.BTG.entries()) {
        let suffix: null | string = null

        switch (svdPerson.BTG_TYP) {
          case '22':
            suffix = 'Insurance'

            const insuranceCompanyName = getCompanyName(svdPerson)
            customTemplateData['address_companyName' + suffix] = insuranceCompanyName
            customTemplateData['insurerCompanyName'] = insuranceCompanyName

            break

          case '23':
            suffix = 'InvoiceRecipient'
            break

          case '24':
            suffix = 'Insurant'

            customTemplateData['address_lastName' + suffix] = svdPerson.BTG_NAME1
            customTemplateData['address_firstName' + suffix] = svdPerson.BTG_NAME2
            break

          case '25':
            suffix = 'Opponent'

            customTemplateData['address_lastName' + suffix] = svdPerson.BTG_NAME1
            customTemplateData['address_surname'] = svdPerson.BTG_NAME1
            customTemplateData['address_firstName' + suffix] = svdPerson.BTG_NAME2
            customTemplateData['address_firstName'] = svdPerson.BTG_NAME2
            break

          case '28':
            suffix = 'Repairer'

            const repairerCompanyName = getCompanyName(svdPerson)
            customTemplateData['address_companyName' + suffix] = repairerCompanyName
            customTemplateData['repairerCompanyName'] = repairerCompanyName
            break

          default:
            break
        }

        if (!!suffix) {
          customTemplateData['address_street' + suffix] = svdPerson.BTG_STRASSE
          customTemplateData['address_zip' + suffix] = svdPerson.BTG_PLZ
          customTemplateData['address_city' + suffix] = svdPerson.BTG_ORT
          customTemplateData['address_country' + suffix] = 'AT'
          customTemplateData['address_email' + suffix] = svdPerson.BTG_EMAIL
          customTemplateData['address_mobilePhone' + suffix] = svdPerson.BTG_HANDY
          customTemplateData['address_companyPhone' + suffix] = svdPerson.BTG_TELEFON

          if (svdPerson.BTG_ZUHANDEN) {
            const split = svdPerson.BTG_ZUHANDEN.split(' - ')
            if (split.length >= 1) {
              const name = split[0].trim()
              const { firstName, lastName } = getFirstAndLastName(name)
              if (!customTemplateData['address_firstName' + suffix]) {
                customTemplateData['address_firstName' + suffix] = firstName
              }
              if (!customTemplateData['address_lastName' + suffix]) {
                customTemplateData['address_lastName' + suffix] = lastName
              }
            }

            if (split.length >= 2) {
              const phoneNumbers = split[1].trim()
              if (phoneNumbers?.length > 0) {
                if (!customTemplateData['address_mobilePhone' + suffix]) {
                  customTemplateData['address_mobilePhone' + suffix] = phoneNumbers
                }
              }
            }

            if (split.length >= 3) {
              const emails = split[2].match(/([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9_-]+)/gi)
              if (emails && emails?.length > 0) {
                if (!customTemplateData['address_email' + suffix]) {
                  customTemplateData['address_email' + suffix] = emails[0]
                }
              }
            }

            if (split.length >= 4) {
              const faxes = split[3].trim()
              if (faxes?.length > 0) {
                customTemplateData['address_fax' + suffix] = faxes
              }
            }
          }
        }
      }
    }

    return Object.entries(customTemplateData)
      .filter((field): field is [key: string, value: string] => !!field[1])
      .map(([key, value]) => ({ key, value: { _value: value, _attr_type: 'xs:string' } }))
  }

  async createContract({
    svd,
    dat5Options,
    initialContractData
  }: {
    svd: string
    dat5Options: DAT5.Options
    initialContractData: {
      username: string
      customerNumber: string
      name: string
      surname: string
      claimNumber: string
      euroSvFilesFolderId: number | null | undefined
      euroSvImagesFolderId: number | null | undefined
      claimNumberMemoFieldName: any
    }
  }) {
    const svdContent = this.parseSvd(svd)

    const Dossier = this.buildDossier(svdContent)
    const customTemplateData = this.buildCustomTemplateData(svdContent, initialContractData)
    const { responseObj } = await DAT5.MyClaimExternalService.createOrUpdateContract(
      {
        Dossier,
        templateData: {
          entry: customTemplateData
        },
        networkType: 'DATAU',
        contractType: 'vro_calculation',
        templateId: 145621
      },
      dat5Options
    )
    return responseObj
  }

  async uploadAttachments({
    contractId,
    attachments,
    dat5Options,
    folderId
  }: {
    attachments: DAT.AttachmentItem[]
    dat5Options: DAT5.Options
    contractId: number
    folderId: number
  }) {
    for (let i = 0; i < attachments.length; i++) {
      const { binaryData, mimeType, fileName } = attachments[i]

      await DAT5.MyClaimExternalService.uploadAttachmentByFolderID(
        {
          contractId,
          attachmentItem: {
            published: new Date().toISOString(),
            uploaded: new Date().toISOString(),
            binaryData,
            mimeType,
            fileName: fileName?.replace(/\//g, ' '),
            documentID: folderId || 0
          }
        },
        dat5Options
      )
    }
  }
}
