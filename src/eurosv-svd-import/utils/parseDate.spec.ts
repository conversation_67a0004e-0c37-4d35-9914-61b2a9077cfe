import { formatDate } from './parseDate'
import { DateTime } from 'luxon'

describe('formatDate', () => {
  it('should handle different formats correctly with date input', () => {
    const date = DateTime.fromISO('2023-11-30T10:00:00.000+00:00').toJSDate() // December 31, 2023
    console.log('date', date)
    expect(formatDate(date)).toBe('2023-12-30T00:00:00.000Z')
    expect(formatDate(date, true)).toBe('30.12.2023')
  })

  it('should handle different formats correctly with string input', () => {
    const dateString = '20240115'
    expect(formatDate(dateString, false)).toBe('2024-01-15T00:00:00.000Z')
    expect(formatDate(dateString, true)).toBe('15.01.2024')
  })

  it('should return undefined with empty input or invalid date', () => {
    expect(formatDate('')).toBeUndefined()
    expect(formatDate(undefined)).toBeUndefined()
    expect(formatDate(null as any)).toBeUndefined()
    expect(formatDate('invalid')).toBeUndefined()
    expect(formatDate(new Date(NaN))).toBeUndefined()
  })
})
