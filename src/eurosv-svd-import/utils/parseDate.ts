import { format, parse } from 'date-fns'

export const parseDate = (value?: string): Date | undefined => {
  if (!value) return

  return parse(value, 'yyyyMMdd', new Date())
}

export const formatDate = (date?: Date | string, dateOnly = false): string | undefined => {
  if (!date) return
  if (!(date instanceof Date)) {
    date = parseDate(date) as Date
  }
  if (isNaN(date.getTime())) return //check InvalidDate

  if (dateOnly) {
    return format(date, 'dd.MM.yyyy')
  } else {
    return format(date, 'yyyy-MM-dd') + 'T00:00:00.000Z'
  }
}
